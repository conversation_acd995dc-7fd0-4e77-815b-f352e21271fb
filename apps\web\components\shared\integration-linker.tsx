"use client";

import { useState, useEffect } from "react";
import { Switch } from "@workspace/ui/components/switch";
import { Label } from "@workspace/ui/components/label";
import {
  Select,
  SelectContent,
  SelectItem,
  SelectTrigger,
  SelectValue,
} from "@workspace/ui/components/select";
import { Badge } from "@workspace/ui/components/badge";
import { CheckCircle, XCircle } from "lucide-react";
import { toast } from "sonner";
import { useQuery, useMutation } from "convex/react";
import { api } from "@workspace/backend/convex/_generated/api";
import { getAvailablePurposes } from "@/lib/utils";

// Types based on the Prisma schema and action responses
interface Integration {
  id: string;
  name: string;
  type: string;
  config: any;
  isActive: boolean;
  organizationId: string;
  createdAt: Date;
  updatedAt: Date;
  createdById?: string | null;
}

interface IntegrationUsage {
  id: string;
  integrationId: string;
  entityType: string;
  entityId: string;
  purpose: string;
  isActive: boolean;
  createdAt: Date;
  updatedAt: Date;
  integration?: Integration;
}

interface IntegrationLinkerProps {
  entityType: string;
  entityId: string;
  purpose: string;
  label?: string;
  description?: string;
  onSuccess?: () => void;
}

export function IntegrationLinker({
  entityType,
  entityId,
  purpose,
  label,
  description,
  onSuccess,
}: IntegrationLinkerProps) {
  const [enabled, setEnabled] = useState(false);
  const [selectedIntegrationId, setSelectedIntegrationId] = useState<
    string | null
  >(null);

  const purposes = getAvailablePurposes();
  const purposeInfo = purposes.find((p) => p.value === purpose);

  // Fetch integrations for purpose using Convex
  const integrations = useQuery(api.settings.integration.getIntegrationsForPurpose,
    entityId ? { purpose } : "skip"
  );

  // Fetch current integration usage using Convex
  const usage = useQuery(api.settings.integrationUsage.getIntegrationUsage,
    entityId ? {
      entityType,
      entityId,
      purpose,
    } : "skip"
  );

  // Use useEffect to handle side effects when usage data changes
  useEffect(() => {
    if (usage) {
      setEnabled(!!usage.isActive);
      setSelectedIntegrationId(usage.integrationId || null);
    } else {
      setEnabled(false);
      setSelectedIntegrationId(null);
    }
  }, [usage]);

  // Convex mutations
  const deleteUsageMutation = useMutation(api.settings.integrationUsage.deleteIntegrationUsage);
  const createUsageMutation = useMutation(api.settings.integrationUsage.createIntegrationUsage);

  const handleUnlink = async (usageId: string) => {
    try {
      await deleteUsageMutation({ id: usageId as any });
      toast.success("Integration unlinked successfully");
      setSelectedIntegrationId(null);
      setEnabled(false);
      onSuccess?.();
    } catch (error) {
      toast.error("Failed to unlink integration");
      setEnabled(true); // Revert the toggle
    }
  };

  const handleLink = async (integrationId: string) => {
    try {
      // Remove existing usage if any
      if (usage?.id) {
        await deleteUsageMutation({ id: usage.id as any });
      }
      await createUsageMutation({
        integrationId: integrationId as any,
        entityType,
        entityId,
        purpose,
        isActive: true,
      });
      toast.success("Integration linked successfully");
      setEnabled(true);
      onSuccess?.();
    } catch (error) {
      toast.error("Failed to link integration");
      setSelectedIntegrationId(null);
    }
  };

  const handleToggle = (checked: boolean) => {
    setEnabled(checked);
    if (!checked && usage?.id) {
      unlinkMutation.mutate(usage.id);
    }
  };

  const handleIntegrationChange = (integrationId: string) => {
    if (!integrationId) return;
    setSelectedIntegrationId(integrationId);
    linkMutation.mutate(integrationId);
  };

  if (integrationsLoading || usageLoading) {
    return (
      <div className="space-y-4">
        <div className="flex items-center justify-between py-2">
          <div>
            <div className="text-sm font-medium">
              {label || purposeInfo?.label || purpose}
            </div>
            <div className="text-xs text-muted-foreground">
              {description || purposeInfo?.description}
            </div>
          </div>
          <div className="h-4 w-8 bg-muted rounded animate-pulse" />
        </div>
      </div>
    );
  }

  return (
    <div className="space-y-4">
      <div className="flex items-center justify-between py-2">
        <div>
          <div className="text-sm font-medium">
            {label || purposeInfo?.label || purpose}
          </div>
          <div className="text-xs text-muted-foreground">
            {description || purposeInfo?.description}
          </div>
        </div>
        <Switch
          checked={enabled}
          onCheckedChange={handleToggle}
          disabled={linkMutation.isPending || unlinkMutation.isPending}
        />
      </div>

      {enabled && (
        <div className="space-y-2">
          <Label>Integration</Label>
          <Select
            value={selectedIntegrationId || ""}
            onValueChange={handleIntegrationChange}
            disabled={linkMutation.isPending || unlinkMutation.isPending}
          >
            <SelectTrigger>
              <SelectValue placeholder="Select an integration" />
            </SelectTrigger>
            <SelectContent>
              {!integrations || integrations.length === 0 ? (
                <SelectItem value="no-integrations" disabled>
                  No integrations available
                </SelectItem>
              ) : (
                integrations &&
                integrations.map((integration: Integration) => (
                  <SelectItem key={integration.id} value={integration.id}>
                    <div className="flex items-center gap-2">
                      <span>{integration.name}</span>
                      {integration.isActive ? (
                        <Badge
                          variant="default"
                          className="bg-green-100 text-green-800 hover:bg-green-100"
                        >
                          <CheckCircle className="h-3 w-3 mr-1" />
                          Active
                        </Badge>
                      ) : (
                        <Badge variant="secondary">
                          <XCircle className="h-3 w-3 mr-1" />
                          Inactive
                        </Badge>
                      )}
                    </div>
                  </SelectItem>
                ))
              )}
            </SelectContent>
          </Select>
          <p className="text-xs text-muted-foreground">
            <a
              href="/settings/integrations"
              className="text-primary hover:underline"
            >
              Manage integrations
            </a>{" "}
            to connect your {purposeInfo?.label?.toLowerCase() || purpose}{" "}
            platform
          </p>
        </div>
      )}
    </div>
  );
}
